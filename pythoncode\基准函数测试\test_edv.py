#!/usr/bin/env python3
"""
EDV函数测试脚本
测试三个不同版本的EDV函数的性能和准确性
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from EDV评估 import main

if __name__ == "__main__":
    try:
        main()
    except FileNotFoundError as e:
        print(f"错误: 网络文件未找到 - {e}")
        print("请确保网络数据文件路径正确")
    except Exception as e:
        print(f"运行时错误: {e}")
        import traceback
        traceback.print_exc()

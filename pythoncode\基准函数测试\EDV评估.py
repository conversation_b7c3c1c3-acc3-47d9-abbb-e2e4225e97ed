import networkx as nx
import numpy as np
import time
from scipy.sparse import csr_matrix


class GEN_GRAPH:
    """Network graph generator and neighbor cache container

    Attributes:
        network_path (str): 网络数据文件路径（包含节点关系的文本文件）
        nx_G (nx.Graph): NetworkX图对象实例
        neighbors (dict): 节点邻居字典缓存{节点: [直接邻居列表]}
    """

    def __init__(self, network_path: str) -> None:
        """初始化图生成器并预生成邻居缓存

        Args:
            network_path: 网络数据文件路径（需符合np.loadtxt格式要求）

        Raises:
            FileNotFoundError: 当指定路径不存在时
            ValueError: 当文件格式不符合要求时
        """
        self.network_path = network_path
        self.nx_G = self.gen_graph()
        self.neighbors = {node: list(self.nx_G.neighbors(node)) for node in self.nx_G.nodes()}

    def gen_graph(self) -> nx.Graph:
        """从边列表文件构建无向图（支持标准边列表格式）

        文件格式要求：
        - 首行为表头（将被自动跳过）
        - 每行包含两个整数节点ID（从第0列和第1列读取）

        Returns:
            生成的NetworkX无向图对象

        Raises:
            FileNotFoundError: 当文件路径不存在时
            ValueError: 当数据列不足或无法转换为整数时
            RuntimeError: 当图构建过程中出现未知错误时
        """
        try:
            G = nx.Graph()
            # 使用numpy加速边数据加载（跳过首行表头）
            edges_data = np.loadtxt(
                self.network_path,
                skiprows=1,    # 忽略标题行
                usecols=[0, 1] # 仅读取前两列作为边
            )
            # 将浮点数据转换为整数节点ID（适用于非负整数ID）
            edges = [(int(u), int(v)) for u, v in edges_data]
            G.add_edges_from(edges)
            return G
        except FileNotFoundError as e:
            raise FileNotFoundError(f"Network file not found: {self.network_path}") from e
        except ValueError as e:
            raise ValueError(f"Invalid data format in {self.network_path}") from e
        except Exception as e:
            raise RuntimeError(f"Graph construction failed: {str(e)}") from e


def EDV_basic(graph, S, p):
    """
    基础版本的影响力评估 (Expected Diffusion Value)
    使用邻接字典和集合操作

    Args:
        graph: NetworkX图对象
        S: 种子节点集合
        p: 传播概率

    Returns:
        float: 估计的影响力值
    """
    # 预生成邻接字典
    adj_dict = {node: set(graph.neighbors(node)) for node in graph.nodes()}
    S = set(S)

    # 计算一阶邻居 (NS_1)，直接使用集合操作
    NS_1 = {neighbor for s in S for neighbor in adj_dict.get(s, set())} - S

    # 快速计算每个邻居的连接数
    influence_sum = 0
    for node in NS_1:
        num_connections = len(adj_dict[node] & S)
        influence_sum += 1 - (1 - p) ** num_connections

    return len(S) + influence_sum

def EDV_matrix(graph, S, p):
    """
    矩阵优化版本的影响力评估 (Expected Diffusion Value)
    使用稀疏矩阵运算加速计算

    Args:
        graph: NetworkX图对象
        S: 种子节点集合
        p: 传播概率

    Returns:
        float: 估计的影响力值
    """
    # 生成节点索引映射（应对非连续节点ID）
    nodes = list(graph.nodes())
    node_index = {n: i for i, n in enumerate(nodes)}
    n = len(nodes)

    # 构建稀疏邻接矩阵（CSR格式加速矩阵运算）
    adj_csr = nx.adjacency_matrix(graph, nodelist=nodes).tocsr()

    # 生成种子节点掩码向量（O(1)时间访问）
    seed_indices = np.array([node_index[s] for s in S if s in node_index], dtype=np.int32)
    if len(seed_indices) == 0:
        return 0.0
    seed_mask = np.zeros(n, dtype=np.int8)
    seed_mask[seed_indices] = 1

    # 稀疏矩阵与种子掩码矩阵乘法获取连接数
    seed_mask_sparse = csr_matrix(seed_mask).T  # 转置为列向量
    conn_counts = adj_csr @ seed_mask_sparse  # 矩阵乘法
    conn_counts = conn_counts.toarray().flatten()  # 转换为普通数组

    # 计算一跳邻居（排除种子节点），确保布尔索引大小一致
    one_hop_mask = (conn_counts > 0) & (seed_mask == 0)  # 直接在此步骤处理
    valid_conn_counts = conn_counts[one_hop_mask]  # 只保留有效的连接数

    # 向量化概率计算
    influence = np.sum(1 - np.power(1 - p, valid_conn_counts))  # 计算影响力

    return len(seed_indices) + influence


def EDV_vectorized(graph, S, p):
    """
    向量化优化版本的影响力评估 (Expected Diffusion Value)
    使用numpy向量化操作提高计算效率

    Args:
        graph: NetworkX图对象
        S: 种子节点集合
        p: 传播概率

    Returns:
        float: 估计的影响力值
    """
    # 生成节点索引映射
    nodes = list(graph.nodes())
    node_index = {n: i for i, n in enumerate(nodes)}
    n = len(nodes)

    # 构建邻接矩阵（布尔类型）
    adj_matrix = nx.adjacency_matrix(graph, nodelist=nodes).astype(bool).tocsr()

    # 生成种子节点掩码
    seed_indices = np.array([node_index[s] for s in S if s in node_index], dtype=np.int32)
    if len(seed_indices) == 0:
        return 0.0

    # 计算每个节点与种子节点的连接数
    conn_counts = adj_matrix[:, seed_indices].sum(axis=1).A1

    # 找到一阶邻居（排除种子节点）
    seed_mask = np.zeros(n, dtype=bool)
    seed_mask[seed_indices] = True
    one_hop_mask = (conn_counts > 0) & (~seed_mask)

    # 向量化计算影响力
    one_hop_counts = conn_counts[one_hop_mask]
    influence = np.sum(1 - np.power(1 - p, one_hop_counts))

    return len(seed_indices) + influence








def IC_vec(g, seed, p, mc=1000):
    """向量化实现的独立级联模型模拟
    
    Args:
        g: NetworkX图对象
        seed: 种子节点集合
        p: 传播概率
        mc: 蒙特卡洛模拟次数
        
    Returns:
        float: 平均影响力扩散范围
    """
    # 预处理节点到索引的映射和邻居列表
    node_list = list(g.nodes())
    node_to_index = {node: idx for idx, node in enumerate(node_list)}
    n = len(node_list)
    
    # 预存每个节点的邻居索引数组（向量化预生成）
    preprocessed_neighbors = [
        np.array([node_to_index[n] for n in g.neighbors(node)], dtype=np.int32)
        for node in node_list
    ]
    
    # 转换种子节点为索引
    seed_indices = np.array([node_to_index[n] for n in seed], dtype=np.int32)
    
    # 预分配内存空间
    influence = np.empty(mc, dtype=np.int32)

    for i in range(mc):
        active = np.zeros(n, dtype=np.bool_)
        active[seed_indices] = True
        newly_active = active.copy()
        
        while np.any(newly_active):
            # 向量化收集激活节点的所有邻居
            current_active = np.flatnonzero(newly_active)
            neighbors = np.concatenate([preprocessed_neighbors[idx] for idx in current_active])
            
            if neighbors.size == 0:
                break
            
            # 生成与neighbors长度匹配的随机数
            rand_values = np.random.rand(len(neighbors))  # 直接生成所需长度的随机数
            
            # 向量化筛选激活节点
            activated = neighbors[rand_values < p]
            unique_activated = np.unique(activated)
            
            # 更新激活状态
            is_new = ~active[unique_activated]
            newly_active_nodes = unique_activated[is_new]
            
            active[newly_active_nodes] = True
            newly_active.fill(False)
            newly_active[newly_active_nodes] = True
        
        influence[i] = active.sum()
    
    return np.mean(influence)


def main():
    """
    主函数：测试三个EDV函数的性能和准确性
    """
    print("=" * 60)
    print("EDV函数性能测试")
    print("=" * 60)

    # 网络文件路径
    network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"

    # 初始化图对象
    print("正在加载网络数据...")
    G = GEN_GRAPH(network_path)
    print(f"网络加载完成: {len(G.nx_G.nodes())} 个节点, {len(G.nx_G.edges())} 条边")

    # 设置参数
    p = 0.05  # 传播概率
    k = 50    # 种子节点数量

    # 选择度中心性最高的k个节点作为种子节点
    print(f"正在选择前 {k} 个度中心性最高的节点...")
    degree_centrality = nx.degree_centrality(G.nx_G)
    sorted_nodes = sorted(degree_centrality.items(),
                         key=lambda x: x[1],
                         reverse=True)
    top_k_nodes = [node for node, _ in sorted_nodes[:k]]
    print(f"种子节点选择完成: {top_k_nodes[:10]}... (显示前10个)")

    print("\n" + "=" * 60)
    print("开始EDV函数测试")
    print("=" * 60)

    # 测试1: 基础版本EDV
    print("\n1. 测试基础版本EDV (EDV_basic):")
    start_time = time.time()
    edv_basic_result = EDV_basic(G.nx_G, top_k_nodes, p)
    edv_basic_time = time.time() - start_time
    print(f"   结果: {edv_basic_result:.4f}")
    print(f"   耗时: {edv_basic_time:.6f} 秒")

    # 测试2: 矩阵优化版本EDV
    print("\n2. 测试矩阵优化版本EDV (EDV_matrix):")
    start_time = time.time()
    edv_matrix_result = EDV_matrix(G.nx_G, top_k_nodes, p)
    edv_matrix_time = time.time() - start_time
    print(f"   结果: {edv_matrix_result:.4f}")
    print(f"   耗时: {edv_matrix_time:.6f} 秒")

    # 测试3: 向量化优化版本EDV
    print("\n3. 测试向量化优化版本EDV (EDV_vectorized):")
    start_time = time.time()
    edv_vectorized_result = EDV_vectorized(G.nx_G, top_k_nodes, p)
    edv_vectorized_time = time.time() - start_time
    print(f"   结果: {edv_vectorized_result:.4f}")
    print(f"   耗时: {edv_vectorized_time:.6f} 秒")

    # 测试4: IC模型模拟（作为基准）
    print("\n4. 测试IC模型模拟 (基准测试):")
    start_time = time.time()
    ic_result = IC_vec(G.nx_G, top_k_nodes, p, mc=1000)
    ic_time = time.time() - start_time
    print(f"   结果: {ic_result:.4f}")
    print(f"   耗时: {ic_time:.6f} 秒")

    print("\n" + "=" * 60)
    print("性能对比分析")
    print("=" * 60)

    # 性能对比
    print("\n时间性能对比:")
    print(f"   基础版本EDV:     {edv_basic_time:.6f} 秒")
    print(f"   矩阵优化EDV:     {edv_matrix_time:.6f} 秒")
    print(f"   向量化优化EDV:   {edv_vectorized_time:.6f} 秒")
    print(f"   IC模型模拟:      {ic_time:.6f} 秒")

    # 加速比计算
    print("\n加速比分析 (相对于基础版本):")
    print(f"   矩阵优化版本:    {edv_basic_time/edv_matrix_time:.2f}x")
    print(f"   向量化优化版本:  {edv_basic_time/edv_vectorized_time:.2f}x")

    # 准确性对比
    print("\n准确性对比 (与IC模拟的相对误差):")
    if ic_result > 0:
        basic_error = abs((edv_basic_result - ic_result)/ic_result)*100
        matrix_error = abs((edv_matrix_result - ic_result)/ic_result)*100
        vectorized_error = abs((edv_vectorized_result - ic_result)/ic_result)*100

        print(f"   基础版本EDV:     {basic_error:.2f}%")
        print(f"   矩阵优化EDV:     {matrix_error:.2f}%")
        print(f"   向量化优化EDV:   {vectorized_error:.2f}%")

    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()